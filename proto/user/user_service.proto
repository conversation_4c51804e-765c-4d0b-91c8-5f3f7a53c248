syntax = "proto3";

package user;

import "user/user.proto";

option java_package = "com.example.user";
option java_multiple_files = true;
option go_package = "github.com/example/bazel-example/proto/user;userpb";

// User service definition
service UserService {
  // Get user by ID
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
}

// Request/Response messages
message GetUserRequest {
  string id = 1;
}

message GetUserResponse {
  UserModel user = 1;
}
