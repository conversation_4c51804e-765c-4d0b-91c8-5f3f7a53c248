module(
    name = "bazel-example",
    version = "1.0.0",
)

# Bazel rules for different languages
bazel_dep(name = "rules_go", version = "0.56.1")
bazel_dep(name = "gazelle", version = "0.45.0")
bazel_dep(name = "rules_java", version = "8.15.1")
bazel_dep(name = "rules_jvm_external", version = "6.8")
bazel_dep(name = "contrib_rules_jvm", version = "0.29.0")
bazel_dep(name = "protobuf", version = "31.1")
# https://github.com/salesforce/rules_spring
bazel_dep(name = "rules_spring", version = "2.6.3")
bazel_dep(name = "grpc-java", version = "1.71.0")

# Go dependencies
go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")

# Java dependencies
maven = use_extension("@rules_jvm_external//:extensions.bzl", "maven")
maven.install(
    repositories = [
            "https://repo1.maven.org/maven2",
        ],
    boms = [
            "org.springframework.boot:spring-boot-dependencies:3.5.4",
            "io.github.danielliu1123:grpc-starter-dependencies:3.5.4",
            "io.grpc:grpc-bom:1.74.0",
            "com.google.protobuf:protobuf-bom:4.31.1",
        ],
    artifacts = [
        # Spring Boot artifacts
        "org.springframework.boot:spring-boot-starter-web",
        "org.springframework.boot:spring-boot-loader",
        "org.springframework.boot:spring-boot-jarmode-tools",
        "org.springframework.boot:spring-boot-loader-tools",
        "org.springframework.boot:spring-boot-starter-test",

        "io.github.danielliu1123:grpc-boot-starter",
        "io.github.danielliu1123:grpc-client-boot-starter",
        "io.github.danielliu1123:grpc-server-boot-starter",

        "com.google.protobuf:protobuf-java",
        "com.google.protobuf:protobuf-java-util",
        "io.grpc:grpc-netty",
        "io.grpc:grpc-netty-shaded",
        "io.grpc:grpc-protobuf",
        "io.grpc:grpc-stub",
        "io.grpc:grpc-services",

        "org.junit.jupiter:junit-jupiter-api",
        "org.junit.platform:junit-platform-launcher",
        "org.junit.platform:junit-platform-reporting",

        # Annotation processors
        "org.projectlombok:lombok",
        # https://github.com/mapstruct/mapstruct
        "org.mapstruct:mapstruct:1.6.3",
        "org.mapstruct:mapstruct-processor:1.6.3",
        # https://mapstruct.org/faq/#Can-I-use-MapStruct-together-with-Project-Lombok
        "org.projectlombok:lombok-mapstruct-binding:0.2.0",
        # https://github.com/entur/mapstruct-spi-protobuf
        "no.entur.mapstruct.spi:protobuf-spi-impl:1.50.0",
    ],
)
use_repo(maven, "maven")