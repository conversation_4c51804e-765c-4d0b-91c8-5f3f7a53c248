version: '3.8'

services:
  # Infrastructure services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: bazel_example
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # Application services
  user-service:
    build:
      context: ../../
      dockerfile: tools/docker/java.Dockerfile
      args:
        SERVICE_TARGET: //services/user/java:user_service
    ports:
      - "8081:8080"
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=*********************************************
      - REDIS_URL=redis://redis:6379

  order-service:
    build:
      context: ../../
      dockerfile: tools/docker/java.Dockerfile
      args:
        SERVICE_TARGET: //services/order/java:order_service
    ports:
      - "8082:8080"
    depends_on:
      - postgres
      - redis
      - user-service
    environment:
      - DATABASE_URL=*********************************************
      - REDIS_URL=redis://redis:6379
      - USER_SERVICE_URL=http://user-service:8080

  gateway:
    build:
      context: ../../
      dockerfile: tools/docker/go.Dockerfile
      args:
        SERVICE_TARGET: //services/gateway/go:gateway
    ports:
      - "8080:8080"
    depends_on:
      - user-service
      - order-service
    environment:
      - USER_SERVICE_URL=http://user-service:8080
      - ORDER_SERVICE_URL=http://order-service:8080

volumes:
  postgres_data:
