load("@protobuf//bazel:proto_library.bzl", "proto_library")
load("@protobuf//bazel:java_proto_library.bzl", "java_proto_library")
load("@rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "proto",
    srcs = glob(["**/*.proto"]),
    strip_import_prefix = "/proto",
    visibility = ["//visibility:public"],
)

java_proto_library(
    name = "proto_java",
    deps = [":proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "proto_go",
    importpath = "github.com/example/bazel-example/proto/common",
    protos = [":proto"],
    visibility = ["//visibility:public"],
)
